# Create: Components and Additions
_A small Create addon I'm making for my modpack, [Create+](https://www.curseforge.com/minecraft/modpacks/create-mod-plus)._

### Done:

- **A cogwheel sequenced assembly recipe**, with its own incomplete cogwheel item to go with it.
- **Recipes** for mod compat (and a change to an existing Create recipe)
- **Inverted Gearshift and Clutch:** Right-clicking a gearshift or clutch with a redstone torch turns them into their inverted versions. (When inverted, no power means they will act like their powered, non-inverted counterparts, and when they are powered by redstone, they act like a shaft, just like their unpowered, non-inverted counterparts.)
- **Brass Gearbox:** has sides you can cover by right-clicking with brass casing in your hand. Covering sides prevents rotation for transferring to and from that side.
- **Ponders** for new blocks

### Kinks in finished stuff:

- **Brass gearbox ponder** has very tiny issues (but my brother didn't notice them, so...)

### Work in progress:

- **Brass chain drive:** works and has textures, but no functionality difference between andesite version.  
It should be able to connect in more than 1 direction, but I have no idea how to implement that.
  (aka this will never happen)

### Planned:

- **Redstone Clock:** A _configurable_ single-block redstone clock, where you choose how often it will output a pulse.

_Feel Free to suggest more, or make a pull request that adds one of these features or even something you came up with!_