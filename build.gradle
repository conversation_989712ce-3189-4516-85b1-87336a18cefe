plugins {
    id 'idea'
    id 'eclipse'
    id 'maven-publish'
    id 'net.neoforged.moddev.legacyforge' version '2.0.96'
}

version = "${mod_version} - ${minecraft_version}"
group = 'net.stormdragon_64.create_ca' // http://maven.apache.org/guides/mini/guide-naming-conventions.html
archivesBaseName = 'create_ca'

java.toolchain.languageVersion = JavaLanguageVersion.of(17)

println "Java: ${System.getProperty 'java.version'}, JVM: ${System.getProperty 'java.vm.version'} (${System.getProperty 'java.vendor'}), Arch: ${System.getProperty 'os.arch'}"


legacyForge {
    version = "${minecraft_version}-${forge_version}"

    parchment {
        minecraftVersion = minecraft_version
        mappingsVersion = parchment_version
    }

    mods {
        create_ca {
            sourceSet sourceSets.main
        }
    }

    runs {
        configureEach {
            systemProperty 'forge.logging.markers', 'REGISTRIES'
            systemProperty 'forge.logging.console.level', 'debug'
        }

        client {
            client()
            gameDirectory = project.file('run')
        }

        server {
            server()
            gameDirectory = project.file('run/server')
        }

        // This run config launches GameTestServer and runs all registered gametests, then exits.
        // By default, the server will crash when no gametests are provided.
        // The gametest system is also enabled by default for other run configs under the /test command.
        gameTestServer {
            type= "gameTestServer"
            //gameDirectory = project.file('run/gametest')
        }

        data {
            data()
            gameDirectory = project.file('run')

            // Specify the modid for data generation, where to output the resulting resource, and where to look for existing resources.
            programArguments.addAll(
                    '--mod', 'create_ca',
                    '--all',
                    '--output', file('src/generated/resources/').toString(),
                    '--existing', file('src/main/resources/').toString()
            )
        }
    }
}

// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    maven { url = "https://maven.createmod.net" } // Create, Ponder, Flywheel
    maven { url = "https://maven.ithundxr.dev/mirror" } // Registrate
    maven { url = "https://raw.githubusercontent.com/Fuzss/modresources/main/maven/" } // ForgeConfigAPIPort
    maven { url = "https://api.modrinth.com/maven" }
}

dependencies {
//main dependencies
    modImplementation("com.simibubi.create:create-${minecraft_version}:${create_version}:slim") { transitive = false }
    modImplementation("net.createmod.ponder:Ponder-Forge-${minecraft_version}:${ponder_version}")
    modCompileOnly("dev.engine-room.flywheel:flywheel-forge-api-${minecraft_version}:${flywheel_version}")
    modRuntimeOnly("dev.engine-room.flywheel:flywheel-forge-${minecraft_version}:${flywheel_version}")
    modImplementation("com.tterrag.registrate:Registrate:${registrate_version}")
    compileOnly(annotationProcessor("io.github.llamalad7:mixinextras-common:${mixinextras_version}"))
    implementation("io.github.llamalad7:mixinextras-forge:${mixinextras_version}")
    annotationProcessor "org.spongepowered:mixin:${mixin_version}:processor"

    if(enable_extras.toBoolean()) {
//QOL for dev
//   modImplementation("maven.modrinth:jei:${jei_version}-forge") //replace with tmrv
        modImplementation("maven.modrinth:tmrv:${tmrv_version}+mc.20.1") //REMEMBER TO CHANGE WHEN UPGRADING TO 1.21.1+, since it doesn't use the minecraft_version property
        modImplementation("maven.modrinth:emi:${emi_version}+${minecraft_version}+forge")
        modImplementation("maven.modrinth:modernfix:${modernfix_version}+mc${minecraft_version}")
        modImplementation("maven.modrinth:embeddium:${embeddium_version}+mc${minecraft_version}")

//Mods with compat additions
        modImplementation("maven.modrinth:berry-good:${berry_good_version}")
        modImplementation("maven.modrinth:blueprint:${blueprint_version}")
    }
}

mixin {
    // MixinGradle Settings
    add sourceSets.main, "mixins.create_ca.refmap.json"
    config 'mixins.create_ca.json'
}

// Example for how to get properties into the manifest for reading at runtime.
jar {
    manifest.attributes([
                "Specification-Title"     : "create_ca",
                "Specification-Vendor"    : "create_caareus",
                "Specification-Version"   : "1", // We are version 1 of ourselves
                "Implementation-Title"    : project.name,
                "Implementation-Version"  : project.jar.archiveVersion,
                "Implementation-Vendor"   : "create_caareus",
                "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ"),
                "MixinConfigs": "mixins.create_ca.json"
        ])
    }

// Example configuration to allow publishing using the maven-publish plugin
// This is the preferred method to reobfuscate your jar file
jar.finalizedBy('reobfJar')
// However if you are in a multi-project build, dev time needs unobfed jar files, so you can delay the obfuscation until publishing by doing
// publish.dependsOn('reobfJar')

publishing {
    publications {
        mavenJava(MavenPublication) {
            artifact jar
        }
    }
    repositories {
        maven {
            url "file://${project.projectDir}/mcmodsrepo"
        }
    }
}


tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}
