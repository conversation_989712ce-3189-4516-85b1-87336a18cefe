{"block.create_ca.adjustable_brass_chain_gearshift": "ʇɟıɥsɹɐǝ⅁ uıɐɥƆ ssɐɹᗺ ǝןqɐʇsnظpⱯ", "block.create_ca.brass_basin": "uısɐᗺ ssɐɹᗺ", "block.create_ca.brass_chain_drive": "ǝʌıɹᗡ uıɐɥƆ ssɐɹᗺ", "block.create_ca.brass_gearbox": "xoqɹɐǝ⅁ ssɐɹᗺ", "block.create_ca.inverted_clutch": "ɥɔʇnןƆ pǝʇɹǝʌuI", "block.create_ca.inverted_gearshift": "ʇɟıɥsɹɐǝ⅁ pǝʇɹǝʌuI", "create_ca.ponder.basin.header": "uısɐᗺ ǝɥʇ uı sɯǝʇI buıssǝɔoɹԀ", "create_ca.ponder.basin.text_1": "buıssǝɔoɹԀ ɹoɟ spınןℲ puɐ sɯǝʇI pןoɥ uɐɔ uısɐᗺ Ɐ", "create_ca.ponder.basin.text_2": "ɯǝɥʇ ɟo ǝpıs ǝɥʇ oʇ ʍoןǝq ʇndʇno oʇ ʎɹʇ suısɐq 'dǝʇs buıssǝɔoɹd ɐ ɹǝʇɟⱯ", "create_ca.ponder.basin.text_3": "ʇǝɔnɐɟ ʇndʇno uɐ ʍoɥs ןןıʍ uısɐᗺ ǝɥʇ 'ʇuǝsǝɹd sı ʇuǝuodɯoɔ pıןɐʌ ɐ uǝɥM", "create_ca.ponder.basin.text_4": "ǝɹǝɥ ǝןqɐɔıןddɐ ǝɹɐ suoıʇdo ɟo ɹǝqɯnu Ɐ", "create_ca.ponder.basin.text_5": "ʍoןǝq ʎɹoʇuǝʌuı ǝɥʇ ʎq ʇɥbnɐɔ ǝq ןןıʍ sʇndʇnO", "create_ca.ponder.basin.text_6": "buıssǝɔoɹd sʇı uı pǝʇɐǝɹɔ sɯǝʇı uıɐʇǝɹ ןןıʍ uısɐᗺ ǝɥʇ 'ʇǝɔnɐɟ ʇndʇno ʇnoɥʇıM", "create_ca.ponder.basin.text_7": "sʇuǝıpǝɹbuı sɐ pǝsn-ǝɹ ǝq pןnoɥs sʇndʇno ɟı ןnɟǝsn ǝq uɐɔ sıɥ⟘", "create_ca.ponder.basin.text_8": "uısɐq ǝɥʇ ɯoɹɟ pǝʇɔɐɹʇxǝ ǝq oʇ ǝʌɐɥ uǝɥʇ ןןıʍ sʇndʇno pǝɹısǝᗡ", "create_ca.ponder.basin.text_9": "sɯǝʇı pǝssǝɔoɹd-un ʇno buıןןnd pıoʌɐ oʇ ʎɹɐssǝɔǝu ǝq ʇɥbıɯ ɹǝʇןıℲ Ɐ", "create_ca.ponder.brass_gearbox.header": "˙xoqɹɐǝ⅁ ssɐɹᗺ ɐ ɟo sʇɟɐɥs ǝɥʇ buıɹǝʌoƆ", "create_ca.ponder.brass_gearbox.text_1": "ʇןnɐɟǝp ʎq xoqɹɐǝb ǝʇısǝpuɐ uɐ ǝʞıן ʎןʇɔɐxǝ sǝʌɐɥǝq xoqɹɐǝb ssɐɹq ǝɥ⟘", "create_ca.ponder.brass_gearbox.text_2": "¡buıʞɔoןq ǝpıs :ǝɹnʇɐǝɟ )ǝןqɐǝןbboʇ( ɐɹʇxǝ uɐ sɐɥ ʇı 'ɹǝʌǝʍoH", "create_ca.ponder.brass_gearbox.text_3": "˙˙˙buısɐɔ ssɐɹq ɥʇıʍ xoqɹɐǝb ssɐɹq ɐ ɟo ǝpıs ǝɥʇ buıʞɔıןɔ ʇɥbıɹ ʎᗺ", "create_ca.ponder.brass_gearbox.text_4": "˙pǝʞɔoןq ǝq ןןıʍ ǝpıs ʇɐɥʇ˙˙˙", "create_ca.ponder.brass_gearbox.text_5": "˙˙˙oʇ buıɹɹǝɟsuɐɹʇ ɯoɹɟ uoıʇɐʇoɹ doʇs ןןıʍ xoqɹɐǝb ssɐɹq ɐ ɟo sǝpıs ǝɥʇ ɟo ǝuo buıʞɔoןᗺ", "create_ca.ponder.brass_gearbox.text_6": "˙xoqɹɐǝb ssɐɹq ɐ ɯoɹɟ puɐ˙˙˙", "create_ca.ponder.brass_gearbox.text_7": "˙uıɐbɐ buısɐɔ ssɐɹq ɥʇıʍ pǝʞɔoןq ǝq oʇ ʇuɐʍ ɹǝbuoן ou noʎ ǝpıs ǝɥʇ ʞɔıןɔ ʇsnظ 'xoqɹɐǝb ssɐɹq ɐ ɟo ǝpıs ǝɥʇ ʞɔoןqun oʇ ɹǝpɹo uI", "create_ca.ponder.chain_drive.header": "sǝʌıɹᗡ uıɐɥƆ ɥʇıʍ ǝɔɹoɟ ןɐuoıʇɐʇoɹ buıʎɐןǝᴚ", "create_ca.ponder.chain_drive.text_1": "ʍoɹ ɐ uı ɹǝɥʇo ɥɔɐǝ oʇ uoıʇɐʇoɹ ʎɐןǝɹ sǝʌıɹᗡ uıɐɥƆ", "create_ca.ponder.chain_drive.text_2": "uoıʇɔǝɹıp ǝɯɐs ǝɥʇ uı ǝʇɐʇoɹ ןןıʍ sıɥʇ ǝʞıן pǝʇɔǝuuoɔ sʇɟɐɥs ןןⱯ", "create_ca.ponder.chain_drive.text_3": "sǝǝɹbǝp 06 ʎq pǝʇɐʇoɹ ǝq uɐɔ ʍoɹ ǝɥʇ ɟo ʇɹɐd ʎuⱯ", "create_ca.ponder.chain_gearshift.header": "sʇɟıɥsɹɐǝ⅁ uıɐɥƆ ɥʇıʍ pǝǝds ןɐuoıʇɐʇoɹ buıןןoɹʇuoƆ", "create_ca.ponder.chain_gearshift.text_1": "sǝʌıɹᗡ uıɐɥƆ ǝʞıן ʎןʇɔɐxǝ ǝʌɐɥǝq sʇɟıɥsɹɐǝ⅁ uıɐɥƆ pǝɹǝʍodu∩", "create_ca.ponder.chain_gearshift.text_2": "pǝןqnop sı ʍoɹ ǝɥʇ uı sǝʌıɹᗡ uıɐɥƆ ɹǝɥʇo oʇ pǝʇʇıɯsuɐɹʇ pǝǝds ǝɥʇ 'pǝɹǝʍoԀ uǝɥM", "create_ca.ponder.chain_gearshift.text_3": "pɐǝʇsuı pǝʌןɐɥ ǝq ןןıʍ pǝǝds sʇı 'ǝɔɹnos ǝɥʇ ʇɐ ʇou sı ʇɟıɥsɹɐǝ⅁ pǝɹǝʍoԀ ǝɥʇ ɹǝʌǝuǝɥM", "create_ca.ponder.chain_gearshift.text_4": "ʇɟıɥsɹɐǝ⅁ pǝɹǝʍoԀ ǝɥʇ ɟo pǝǝds ǝɥʇ xᄅ ʇɐ unɹ sʎɐʍןɐ ʍoɹ ǝɥʇ uı sǝʌıɹᗡ uıɐɥƆ 'sǝsɐɔ ɥʇoq uI", "create_ca.ponder.chain_gearshift.text_5": "ᄅ puɐ Ɩ uǝǝʍʇǝq ʎןǝsıɔǝɹd ǝɹoɯ pǝʇsnظpɐ ǝq uɐɔ oıʇɐɹ ǝɥʇ 's<PERSON><PERSON><PERSON><PERSON><PERSON> boןɐuɐ buıs∩", "create_ca.ponder.chain_gearshift.text_6": "WԀᴚ ᄅƖ", "create_ca.ponder.gearbox.header": "sǝxoqɹɐǝ⅁ buısn ǝɔɹoɟ ןɐuoıʇɐʇoɹ buıʎɐןǝᴚ", "create_ca.ponder.gearbox.text_1": "ʎןʞɔınb ʎʞןnq ʇǝb uɐɔ uoıʇɐʇoɹ ɟo sǝxɐ uǝǝʍʇǝq buıdɯnſ", "create_ca.ponder.gearbox.text_2": "dnʇǝs sıɥʇ ɟo ʇuǝןɐʌınbǝ ʇɔɐdɯoɔ ǝɹoɯ ǝɥʇ sı xoqɹɐǝb Ɐ", "create_ca.ponder.gearbox.text_3": "suoıʇɔǝɹıp pǝɹoɹɹıɯ uı ǝʇɐʇoɹ sɹǝuɹoɔ punoɹɐ sʇɟɐɥS", "create_ca.ponder.gearbox.text_4": "pǝsɹǝʌǝɹ ǝq ןןıʍ suoıʇɔǝuuoɔ ʇɥbıɐɹʇS", "create_ca.ponder.inverted_clutch.header": "sǝɥɔʇnןƆ pǝʇɹǝʌuI buısn puɐ oʇ buıʇɹǝʌuoƆ", "create_ca.ponder.inverted_clutch.text_1": "˙sǝsɐɔ ǝsn ןןɐ ʇıɟ ʇ,usǝop ʇı 'ןnɟǝsn sı ɥɔʇnןɔ ǝɥʇ ǝןıɥM", "create_ca.ponder.inverted_clutch.text_10": "˙§pǝɹǝʍod sıu§ ʇı uǝɥʍ uoıʇɐʇoɹ sʎɐןǝɹ ʎןdɯıs puɐ˙˙˙", "create_ca.ponder.inverted_clutch.text_11": "¡ɹǝıןɹɐǝ ɯoɹɟ ǝןdɯɐxǝ ǝɥʇ uı pǝʇuɐʍ noʎ ǝʞıן ʇsnſ", "create_ca.ponder.inverted_clutch.text_12": "˙˙˙ǝɹoɯʎuɐ dnʇǝs ɥɔɹoʇ ǝuoʇspǝɹ ʎʞןnq ʇɐɥʇ pǝǝu ʇ,uop ǝʍ ʇɐɥʇ uɐǝɯ sıɥʇ sǝop ʎןuo ʇoN", "create_ca.ponder.inverted_clutch.text_13": "¡ɹǝɥʇɹnɟ sbuıɥʇ buıʇɔɐdɯoɔ 'uoıʇɐʇoɹ ʇuǝʌǝɹd oʇ ʇsnظ ɥɔʇnןɔ ǝɥʇ ɹǝʍod oʇ pǝǝu ʇ,uop noʎ ʇɐɥʇ suɐǝɯ osןɐ ʇı˙˙˙", "create_ca.ponder.inverted_clutch.text_14": "¡ǝuo pǝǝu ɹǝbuoן ou noʎ ǝɔuıs 'pǝɹǝʍod buıǝq ǝɔɹnos ɹǝʍod ǝɥʇ punoɹɐ buıɥʇʎɹǝʌǝ ɟo ɯǝןqoɹd ǝɥʇ sǝxıɟ osןɐ ʇı ʇnq 'ǝɔɐds ɟo ʞɔoןq ssǝן Ɩ ʇsɐǝן ʇɐ ǝʞɐʇ ɥɔʇnןɔ pǝʇɹǝʌuı ǝɥʇ ןןıʍ ʎןuo ʇou ʇɐɥʇ suɐǝɯ sıɥ⟘", "create_ca.ponder.inverted_clutch.text_2": "¿ɹ§pǝɹǝʍod sıu§ ʇı uǝɥʍ ʇɟɐɥs ɐ ǝʞıן sʇɔɐ puɐ 'ɹ§pǝɹǝʍod ʇouu§ uǝɥʍ uoıʇɐʇoɹ sʞɔoןq ʇɐɥʇ ɥɔʇnןɔ ɐ pǝʇuɐʍ noʎ ɟı ʇɐɥʍ 'ǝןdɯɐxǝ ɹoℲ", "create_ca.ponder.inverted_clutch.text_3": "¿ɥɔʇnןɔ ɐ ɟo ɯɹoɟ pǝʇɹǝʌuı uɐ ʇuɐʍ noʎ 'spɹoʍ ɹǝɥʇo uı ɹO", "create_ca.ponder.inverted_clutch.text_4": "˙˙˙ʇɐɥʇ ʇsnظ op oʇ ɥɔɹoʇ ǝuoʇspǝɹ ɐ ǝsn pןnoɔ noʎ ǝןıɥM", "create_ca.ponder.inverted_clutch.text_5": "˙buıʞɐɯ ɹnoʎ ǝuıɥɔɐɯ ǝɥʇ uo buıpuǝdǝp ʎʞןnq pǝɹǝpısuoɔ ǝq ʎןısɐǝ uɐɔ sıɥ⟘", "create_ca.ponder.inverted_clutch.text_6": "˙ʎɐןd oʇuı sǝɯoɔ ɥɔʇnןɔ pǝʇɹǝʌuı ǝɥʇ ǝɹǝɥʍ sı sıɥ⟘", "create_ca.ponder.inverted_clutch.text_7": "˙˙˙ɥɔɹoʇ ǝuoʇspǝɹ ɐ ɥʇıʍ ɥɔʇnןɔ ɐ buıʞɔıןɔ ʇɥbıɹ ʎᗺ", "create_ca.ponder.inverted_clutch.text_8": "˙ɥɔʇnןɔ pǝʇɹǝʌuı uɐ oʇuı pǝʇɹǝʌuoɔ ǝq ןןıʍ ʇı˙˙˙", "create_ca.ponder.inverted_clutch.text_9": "˙˙˙ɹ§pǝɹǝʍod ʇouu§ uǝɥʍ uoıʇɐʇoɹ sʇuǝʌǝɹd ɥɔʇnןɔ pǝʇɹǝʌuı uⱯ", "create_ca.ponder.inverted_gearshift.header": "sʇɟıɥsɹɐǝ⅁ pǝʇɹǝʌuI buısn puɐ oʇ buıʇɹǝʌuoƆ", "create_ca.ponder.inverted_gearshift.text_1": "˙sǝsɐɔ ǝsn ןןɐ ʇıɟ ʇ,usǝop ʇı 'ןnɟǝsn sı ʇɟıɥsɹɐǝb ǝɥʇ ǝןıɥM", "create_ca.ponder.inverted_gearshift.text_10": "˙§pǝɹǝʍod sıu§ ʇı uǝɥʍ uoıʇɐʇoɹ sʎɐןǝɹ ʎןdɯıs puɐ˙˙˙", "create_ca.ponder.inverted_gearshift.text_11": "¡ɹǝıןɹɐǝ ɯoɹɟ ǝןdɯɐxǝ ǝɥʇ uı pǝʇuɐʍ noʎ ǝʞıן ʇsnſ", "create_ca.ponder.inverted_gearshift.text_12": "˙˙˙ǝɹoɯʎuɐ dnʇǝs ɥɔɹoʇ ǝuoʇspǝɹ ʎʞןnq ʇɐɥʇ pǝǝu ʇ,uop ǝʍ ʇɐɥʇ uɐǝɯ sıɥʇ sǝop ʎןuo ʇoN", "create_ca.ponder.inverted_gearshift.text_13": "¡ɹǝɥʇɹnɟ sbuıɥʇ buıʇɔɐdɯoɔ 'uoıʇɐʇoɹ ɟo uoıʇɔǝɹıp ǝɥʇ ǝbuɐɥɔ oʇ ʇsnظ ʇɟıɥsɹɐǝb ǝɥʇ ɹǝʍod oʇ pǝǝu ʇ,uop noʎ ʇɐɥʇ suɐǝɯ osןɐ ʇı˙˙˙", "create_ca.ponder.inverted_gearshift.text_14": "¡ǝuo pǝǝu ɹǝbuoן ou noʎ ǝɔuıs 'pǝɹǝʍod buıǝq ǝɔɹnos ɹǝʍod ǝɥʇ punoɹɐ buıɥʇʎɹǝʌǝ ɟo ɯǝןqoɹd ǝɥʇ sǝxıɟ osןɐ ʇı ʇnq 'ǝɔɐds ɟo ʞɔoןq ssǝן Ɩ ʇsɐǝן ʇɐ ǝʞɐʇ ʇɟıɥsɹɐǝb pǝʇɹǝʌuı ǝɥʇ ןןıʍ ʎןuo ʇou ʇɐɥʇ suɐǝɯ sıɥ⟘", "create_ca.ponder.inverted_gearshift.text_2": "¿ɹ§pǝɹǝʍod sıu§ ʇı uǝɥʍ ʇɟɐɥs ɐ ǝʞıן sʇɔɐ puɐ 'ɹ§pǝɹǝʍod ʇouu§ uǝɥʍ uoıʇɐʇoɹ ɟo uoıʇɔǝɹıp ǝɥʇ sǝsɹǝʌǝɹ ʇɐɥʇ ʇɟıɥsɹɐǝb ɐ pǝʇuɐʍ noʎ ɟı ʇɐɥʍ 'ǝןdɯɐxǝ ɹoℲ", "create_ca.ponder.inverted_gearshift.text_3": "¿ʇɟıɥsɹɐǝb ɐ ɟo ɯɹoɟ pǝʇɹǝʌuı uɐ ʇuɐʍ noʎ 'spɹoʍ ɹǝɥʇo uı ɹO", "create_ca.ponder.inverted_gearshift.text_4": "˙˙˙ʇɐɥʇ ʇsnظ op oʇ ɥɔɹoʇ ǝuoʇspǝɹ ɐ ǝsn pןnoɔ noʎ ǝןıɥM", "create_ca.ponder.inverted_gearshift.text_5": "˙buıʞɐɯ ɹnoʎ ǝuıɥɔɐɯ ǝɥʇ uo buıpuǝdǝp ʎʞןnq pǝɹǝpısuoɔ ǝq ʎןısɐǝ uɐɔ sıɥ⟘", "create_ca.ponder.inverted_gearshift.text_6": "˙ʎɐןd oʇuı sǝɯoɔ ʇɟıɥsɹɐǝb pǝʇɹǝʌuı ǝɥʇ ǝɹǝɥʍ sı sıɥ⟘", "create_ca.ponder.inverted_gearshift.text_7": "˙˙˙ɥɔɹoʇ ǝuoʇspǝɹ ɐ ɥʇıʍ ʇɟıɥsɹɐǝb ɐ buıʞɔıןɔ ʇɥbıɹ ʎᗺ", "create_ca.ponder.inverted_gearshift.text_8": "˙ʇɟıɥsɹɐǝb pǝʇɹǝʌuı uɐ oʇuı pǝʇɹǝʌuoɔ ǝq ןןıʍ ʇı˙˙˙", "create_ca.ponder.inverted_gearshift.text_9": "˙˙˙ɹ§pǝɹǝʍod ʇouu§ uǝɥʍ uoıʇɔǝɹıp uoıʇɐʇoɹ sǝsɹǝʌǝɹ ʇɟıɥsɹɐǝb pǝʇɹǝʌuı uⱯ", "create_ca.ponder.mechanical_mixer.header": "ɹǝxıW ןɐɔıuɐɥɔǝW ǝɥʇ ɥʇıʍ sɯǝʇI buıssǝɔoɹԀ", "create_ca.ponder.mechanical_mixer.text_1": "pǝʇɐɯoʇnɐ ǝq uɐɔ sǝdıɔǝᴚ buıʇɟɐɹƆ ǝɯos 'uısɐᗺ puɐ ɹǝxıW ɐ ɥʇıM", "create_ca.ponder.mechanical_mixer.text_2": "sǝuo ɐɹʇxǝ ǝןdnoɔ ɐ snןd 'ǝdıɔǝᴚ buıʇɟɐɹƆ ssǝןǝdɐɥS ʎuɐ ǝpnןɔuı sǝdıɔǝɹ ǝןqɐןıɐʌⱯ", "create_ca.ponder.mechanical_mixer.text_3": "ɹǝuɹnᗺ ǝzɐןᗺ ɐ ɟo ʇɐǝɥ ǝɥʇ ǝɹınbǝɹ ʎɐɯ sǝdıɔǝɹ ǝsoɥʇ ɟo ǝɯoS", "create_ca.ponder.mechanical_mixer.text_4": "˙buıʇɔıןɟuoɔ ǝɹɐ sǝdıɔǝɹ oʍʇ ǝsɐɔ uı pǝsn ǝq uɐɔ ʇoןs ɹǝʇןıɟ ǝɥ⟘", "create_ca.ponder.mechanical_press_compacting.header": "ssǝɹԀ ןɐɔıuɐɥɔǝW ǝɥʇ ɥʇıʍ sɯǝʇı buıʇɔɐdɯoƆ", "create_ca.ponder.mechanical_press_compacting.text_1": "pǝʇɔɐdɯoƆ ǝq oʇ ɯǝɥʇ ǝsnɐɔ ןןıʍ uısɐᗺ ɐ uı pןǝɥ sɯǝʇı buıssǝɹԀ", "create_ca.ponder.mechanical_press_compacting.text_2": "sǝuo ɐɹʇxǝ ǝןdnoɔ ɐ snןd 'ǝdıɔǝᴚ buıʇɟɐɹƆ ƐxƐ ɹo ᄅxᄅ pǝןןıɟ ʎuɐ sǝpnןɔuı buıʇɔɐdɯoƆ", "create_ca.ponder.mechanical_press_compacting.text_3": "ɹǝuɹnᗺ ǝzɐןᗺ ɐ ɟo ʇɐǝɥ ǝɥʇ ǝɹınbǝɹ ʎɐɯ sǝdıɔǝɹ ǝsoɥʇ ɟo ǝɯoS", "create_ca.ponder.mechanical_press_compacting.text_4": "˙buıʇɔıןɟuoɔ ǝɹɐ sǝdıɔǝɹ oʍʇ ǝsɐɔ uı pǝsn ǝq uɐɔ ʇoןs ɹǝʇןıɟ ǝɥ⟘", "item.create_ca.incomplete_cogwheel": "ןǝǝɥʍboƆ ǝʇǝןdɯoɔuI", "item.create_ca.vertical_brass_gearbox": "xoqɹɐǝ⅁ ssɐɹᗺ ןɐɔıʇɹǝΛ", "itemGroup.create_ca": "suoıʇıppⱯ puɐ sʇuǝuodɯoƆ :ǝʇɐǝɹƆ"}