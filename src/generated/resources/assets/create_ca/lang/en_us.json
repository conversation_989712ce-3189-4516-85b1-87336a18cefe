{"block.create_ca.adjustable_brass_chain_gearshift": "Adjustable Brass Chain Gearshift", "block.create_ca.brass_basin": "Brass Basin", "block.create_ca.brass_chain_drive": "Brass Chain Drive", "block.create_ca.brass_gearbox": "Brass Gearbox", "block.create_ca.inverted_clutch": "Inverted Clutch", "block.create_ca.inverted_gearshift": "Inverted Gearshift", "create_ca.ponder.basin.header": "Processing Items in the Basin", "create_ca.ponder.basin.text_1": "A Basin can hold Items and Fluids for Processing", "create_ca.ponder.basin.text_2": "After a processing step, basins try to output below to the side of them", "create_ca.ponder.basin.text_3": "When a valid component is present, the Basin will show an output faucet", "create_ca.ponder.basin.text_4": "A number of options are applicable here", "create_ca.ponder.basin.text_5": "Outputs will be caught by the inventory below", "create_ca.ponder.basin.text_6": "Without output faucet, the Basin will retain items created in its processing", "create_ca.ponder.basin.text_7": "This can be useful if outputs should be re-used as ingredients", "create_ca.ponder.basin.text_8": "Desired outputs will then have to be extracted from the basin", "create_ca.ponder.basin.text_9": "A Filter might be necessary to avoid pulling out un-processed items", "create_ca.ponder.brass_gearbox.header": "Covering the shafts of a Brass Gearbox.", "create_ca.ponder.brass_gearbox.text_1": "The brass gearbox behaves exactly like an andesite gearbox by default", "create_ca.ponder.brass_gearbox.text_2": "However, it has an extra (toggleable) feature: side blocking!", "create_ca.ponder.brass_gearbox.text_3": "By right clicking the side of a brass gearbox with brass casing...", "create_ca.ponder.brass_gearbox.text_4": "...that side will be blocked.", "create_ca.ponder.brass_gearbox.text_5": "Blocking one of the sides of a brass gearbox will stop rotation from transferring to...", "create_ca.ponder.brass_gearbox.text_6": "...and from a brass gearbox.", "create_ca.ponder.brass_gearbox.text_7": "In order to unblock the side of a brass gearbox, just click the side you no longer want to be blocked with brass casing again.", "create_ca.ponder.chain_drive.header": "Relaying rotational force with Chain Drives", "create_ca.ponder.chain_drive.text_1": "Chain Drives relay rotation to each other in a row", "create_ca.ponder.chain_drive.text_2": "All shafts connected like this will rotate in the same direction", "create_ca.ponder.chain_drive.text_3": "Any part of the row can be rotated by 90 degrees", "create_ca.ponder.chain_gearshift.header": "Controlling rotational speed with Chain Gearshifts", "create_ca.ponder.chain_gearshift.text_1": "Unpowered Chain Gearshifts behave exactly like Chain Drives", "create_ca.ponder.chain_gearshift.text_2": "When Powered, the speed transmitted to other Chain Drives in the row is doubled", "create_ca.ponder.chain_gearshift.text_3": "Whenever the Powered Gearshift is not at the source, its speed will be halved instead", "create_ca.ponder.chain_gearshift.text_4": "In both cases, Chain Drives in the row always run at 2x the speed of the Powered Gearshift", "create_ca.ponder.chain_gearshift.text_5": "Using analog signals, the ratio can be adjusted more precisely between 1 and 2", "create_ca.ponder.chain_gearshift.text_6": "12 RPM", "create_ca.ponder.gearbox.header": "Relaying rotational force using Gearboxes", "create_ca.ponder.gearbox.text_1": "Jumping between axes of rotation can get bulky quickly", "create_ca.ponder.gearbox.text_2": "A gearbox is the more compact equivalent of this setup", "create_ca.ponder.gearbox.text_3": "Shafts around corners rotate in mirrored directions", "create_ca.ponder.gearbox.text_4": "Straight connections will be reversed", "create_ca.ponder.inverted_clutch.header": "Converting to and using Inverted Clutches", "create_ca.ponder.inverted_clutch.text_1": "While the clutch is useful, it doesn't fit all use cases.", "create_ca.ponder.inverted_clutch.text_10": "...and simply relays rotation when it §nis powered§.", "create_ca.ponder.inverted_clutch.text_11": "Just like you wanted in the example from earlier!", "create_ca.ponder.inverted_clutch.text_12": "Not only does this mean that we don't need that bulky redstone torch setup anymore...", "create_ca.ponder.inverted_clutch.text_13": "...it also means that you don't need to power the clutch just to prevent rotation, compacting things further!", "create_ca.ponder.inverted_clutch.text_14": "This means that not only will the inverted clutch take at least 1 less block of space, but it also fixes the problem of everything around the power source being powered, since you no longer need one!", "create_ca.ponder.inverted_clutch.text_2": "For example, what if you wanted a clutch that blocks rotation when §nnot powered§r, and acts like a shaft when it §nis powered§r?", "create_ca.ponder.inverted_clutch.text_3": "Or in other words, you want an inverted form of a clutch?", "create_ca.ponder.inverted_clutch.text_4": "While you could use a redstone torch to do just that...", "create_ca.ponder.inverted_clutch.text_5": "This can easily be considered bulky depending on the machine your making.", "create_ca.ponder.inverted_clutch.text_6": "This is where the inverted clutch comes into play.", "create_ca.ponder.inverted_clutch.text_7": "By right clicking a clutch with a redstone torch...", "create_ca.ponder.inverted_clutch.text_8": "...it will be converted into an inverted clutch.", "create_ca.ponder.inverted_clutch.text_9": "An inverted clutch prevents rotation when §nnot powered§r...", "create_ca.ponder.inverted_gearshift.header": "Converting to and using Inverted Gearshifts", "create_ca.ponder.inverted_gearshift.text_1": "While the gearshift is useful, it doesn't fit all use cases.", "create_ca.ponder.inverted_gearshift.text_10": "...and simply relays rotation when it §nis powered§.", "create_ca.ponder.inverted_gearshift.text_11": "Just like you wanted in the example from earlier!", "create_ca.ponder.inverted_gearshift.text_12": "Not only does this mean that we don't need that bulky redstone torch setup anymore...", "create_ca.ponder.inverted_gearshift.text_13": "...it also means that you don't need to power the gearshift just to change the direction of rotation, compacting things further!", "create_ca.ponder.inverted_gearshift.text_14": "This means that not only will the inverted gearshift take at least 1 less block of space, but it also fixes the problem of everything around the power source being powered, since you no longer need one!", "create_ca.ponder.inverted_gearshift.text_2": "For example, what if you wanted a gearshift that reverses the direction of rotation when §nnot powered§r, and acts like a shaft when it §nis powered§r?", "create_ca.ponder.inverted_gearshift.text_3": "Or in other words, you want an inverted form of a gearshift?", "create_ca.ponder.inverted_gearshift.text_4": "While you could use a redstone torch to do just that...", "create_ca.ponder.inverted_gearshift.text_5": "This can easily be considered bulky depending on the machine your making.", "create_ca.ponder.inverted_gearshift.text_6": "This is where the inverted gearshift comes into play.", "create_ca.ponder.inverted_gearshift.text_7": "By right clicking a gearshift with a redstone torch...", "create_ca.ponder.inverted_gearshift.text_8": "...it will be converted into an inverted gearshift.", "create_ca.ponder.inverted_gearshift.text_9": "An inverted gearshift reverses rotation direction when §nnot powered§r...", "create_ca.ponder.mechanical_mixer.header": "Processing Items with the Mechanical Mixer", "create_ca.ponder.mechanical_mixer.text_1": "With a Mixer and Basin, some Crafting Recipes can be automated", "create_ca.ponder.mechanical_mixer.text_2": "Available recipes include any <PERSON><PERSON><PERSON>ess Crafting Recipe, plus a couple extra ones", "create_ca.ponder.mechanical_mixer.text_3": "Some of those recipes may require the heat of a Blaze Burner", "create_ca.ponder.mechanical_mixer.text_4": "The filter slot can be used in case two recipes are conflicting.", "create_ca.ponder.mechanical_press_compacting.header": "Compacting items with the Mechanical Press", "create_ca.ponder.mechanical_press_compacting.text_1": "Pressing items held in a Basin will cause them to be Compacted", "create_ca.ponder.mechanical_press_compacting.text_2": "Compacting includes any filled 2x2 or 3x3 Crafting Recipe, plus a couple extra ones", "create_ca.ponder.mechanical_press_compacting.text_3": "Some of those recipes may require the heat of a Blaze Burner", "create_ca.ponder.mechanical_press_compacting.text_4": "The filter slot can be used in case two recipes are conflicting.", "item.create_ca.incomplete_cogwheel": "Incomplete Cogwheel", "item.create_ca.vertical_brass_gearbox": "Vertical Brass Gearbox", "itemGroup.create_ca": "Create: Components and Additions"}