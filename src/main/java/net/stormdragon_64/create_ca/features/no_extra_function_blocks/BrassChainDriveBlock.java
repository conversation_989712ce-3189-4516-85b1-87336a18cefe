package net.stormdragon_64.create_ca.features.no_extra_function_blocks;

import com.simibubi.create.content.kinetics.base.KineticBlockEntity;
import com.simibubi.create.content.kinetics.chainDrive.ChainDriveBlock;
import net.minecraft.world.level.block.entity.BlockEntityType;
import net.stormdragon_64.create_ca.ModBlockEntities;


public class Brass<PERSON>hain<PERSON>rive<PERSON><PERSON> extends ChainDriveBlock {
    public BrassChainDriveBlock(Properties properties) {
        super(properties);
    }


    @Override
    public BlockEntityType<? extends KineticBlockEntity> getBlockEntityType() {
        return ModBlockEntities.BRASS_CHAIN_DRIVE.get();
    }

}
